<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存清除测试页面</title>
    
    <!-- 强制禁用缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #1a1a1a;
            color: #40e0d0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #40e0d0;
        }
        
        h1 {
            text-align: center;
            color: #40e0d0;
            text-shadow: 0 0 10px rgba(64, 224, 208, 0.5);
        }
        
        .test-info {
            background: #333333;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 3px solid #40e0d0;
        }
        
        .button {
            background: #40e0d0;
            color: #1a1a1a;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }
        
        .button:hover {
            background: #5af0e0;
        }
        
        .timestamp {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 缓存清除测试页面</h1>
        
        <div class="test-info">
            <h3>📋 测试说明</h3>
            <p>此页面用于测试字体展示页面的缓存清除效果。</p>
            <p><strong>页面更新时间：</strong> 2025-08-03</p>
            <p><strong>版本：</strong> v2.0 - 大间隔分行显示版</p>
        </div>
        
        <div class="test-info">
            <h3>🎯 更新内容</h3>
            <ul>
                <li>✅ 增加字体卡片间距（12px → 24px）</li>
                <li>✅ 添加分隔符（渐变线条）</li>
                <li>✅ 中文字体改为分行显示（英文第一行，中文第二行）</li>
                <li>✅ 添加缓存控制meta标签</li>
                <li>✅ 页面标题添加版本和时间戳</li>
            </ul>
        </div>
        
        <div class="test-info">
            <h3>🔗 测试链接</h3>
            <p>点击下方链接访问更新后的字体展示页面：</p>
            <a href="/test/fonts-starry-fixed.html?v=2.0&t=20250803" class="button" target="_blank">
                打开字体展示页面 (带版本参数)
            </a>
            <a href="/test/fonts-starry-fixed.html" class="button" target="_blank">
                打开字体展示页面 (普通链接)
            </a>
        </div>
        
        <div class="test-info">
            <h3>🛠️ 缓存清除方法</h3>
            <ol>
                <li><strong>强制刷新：</strong> Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac)</li>
                <li><strong>清除浏览器缓存：</strong> F12 → Network → 勾选 "Disable cache"</li>
                <li><strong>隐私模式：</strong> 使用浏览器的隐私/无痕模式访问</li>
                <li><strong>URL参数：</strong> 在URL后添加 ?v=2.0&t=20250803</li>
            </ol>
        </div>
        
        <div class="test-info">
            <h3>✅ 验证要点</h3>
            <p>访问字体展示页面后，请检查以下内容：</p>
            <ul>
                <li>页面标题是否显示 "v2.0" 和 "更新: 2025-08-03"</li>
                <li>字体卡片之间是否有更大的间距</li>
                <li>字体卡片之间是否有淡青色分隔线</li>
                <li>中文字体（字小魂勾玉行书、字小魂三分行楷、字魂行云飞白体）是否为分行显示</li>
                <li>页面底部是否显示更新信息</li>
            </ul>
        </div>
        
        <div class="timestamp">
            <p>🕒 测试页面生成时间：<span id="currentTime"></span></p>
        </div>
    </div>
    
    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');
        
        // 页面加载完成提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('缓存清除测试页面加载完成 - ' + new Date().toISOString());
        });
    </script>
</body>
</html>
