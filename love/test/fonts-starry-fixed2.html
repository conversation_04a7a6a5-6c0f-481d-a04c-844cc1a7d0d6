<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Love Project - 字体展示测试页面 (更新: 2025-08-03)</title>

    <!-- 缓存控制 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/src/client/assets/images/favicon.svg">
    <link rel="alternate icon" href="/src/client/assets/images/favicon.svg">
    <link rel="mask-icon" href="/src/client/assets/images/favicon.svg" color="#ff6b9d">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #1a1a1a;
            min-height: 100vh;
            color: #40e0d0;
            line-height: 1.4;
            position: relative;
            overflow-x: hidden;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 10px;
        }

        .header {
            text-align: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 6px;
            border: 1px solid #40e0d0;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #40e0d0;
            text-shadow: 0 0 10px rgba(64, 224, 208, 0.5);
        }

        .header p {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 10px;
            color: #40e0d0;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: #333333;
            padding: 6px 12px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #40e0d0;
        }

        .stat-number {
            font-size: 1rem;
            font-weight: bold;
            color: #40e0d0;
            text-shadow: 0 0 5px rgba(64, 224, 208, 0.5);
        }

        .stat-label {
            font-size: 0.7rem;
            opacity: 0.7;
            margin-top: 1px;
            color: #40e0d0;
        }

        .font-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 8px;
            margin-bottom: 20px;
        }

        .font-card {
            background: #2a2a2a;
            border-radius: 4px;
            padding: 12px;
            border: 1px solid #40e0d0;
            transition: all 0.3s ease;
            margin-bottom: 24px;
            position: relative;
        }

        /* 字体卡片分隔符 */
        .font-card:not(:last-child)::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 1px;
            background: linear-gradient(90deg, transparent, #40e0d0, transparent);
            opacity: 0.3;
        }

        .font-card:hover {
            border-color: #40e0d0;
            box-shadow: 0 0 15px rgba(64, 224, 208, 0.3);
        }

        .font-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #40e0d0;
        }

        .font-name {
            font-size: 0.95rem;
            font-weight: 600;
            color: #40e0d0;
            text-shadow: 0 0 5px rgba(64, 224, 208, 0.3);
        }

        .font-info {
            font-size: 0.7rem;
            opacity: 0.8;
            text-align: right;
            color: #40e0d0;
        }

        .font-samples {
            margin-top: 6px;
        }

        .sample-group {
            margin-bottom: 8px;
        }

        .sample-label {
            font-size: 0.7rem;
            color: #888;
            margin-bottom: 3px;
            font-weight: 500;
        }

        .sample-text {
            padding: 8px 12px;
            background: #333333;
            border-radius: 3px;
            border-left: 3px solid #40e0d0;
            margin-bottom: 12px;
            line-height: 1.6;
            color: #40e0d0;
            word-spacing: 0.2em;
            letter-spacing: 0.02em;
        }

        .sample-separator {
            text-align: center;
            margin: 6px 0;
            color: #40e0d0;
            opacity: 0.6;
            font-size: 0.8rem;
        }

        .weight-demo {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }

        .weight-item {
            background: #333333;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 0.7rem;
            border: 1px solid #40e0d0;
            color: #40e0d0;
        }

        /* 字体样式定义 */
        .courgette { font-family: 'Courgette', cursive; }
        .great-vibes { font-family: 'Great Vibes', cursive; }
        .dancing-script { font-family: 'Dancing Script', cursive; }
        .dancing-script-bold { font-family: 'Dancing Script', cursive; font-weight: 700; }
        .poppins-light { font-family: 'Poppins', sans-serif; font-weight: 300; }
        .poppins { font-family: 'Poppins', sans-serif; font-weight: 400; }
        .poppins-medium { font-family: 'Poppins', sans-serif; font-weight: 500; }
        .poppins-semibold { font-family: 'Poppins', sans-serif; font-weight: 600; }
        .poppins-bold { font-family: 'Poppins', sans-serif; font-weight: 700; }
        .inter-light { font-family: 'Inter', sans-serif; font-weight: 300; }
        .inter { font-family: 'Inter', sans-serif; font-weight: 400; }
        .inter-medium { font-family: 'Inter', sans-serif; font-weight: 500; }
        .inter-semibold { font-family: 'Inter', sans-serif; font-weight: 600; }
        .inter-bold { font-family: 'Inter', sans-serif; font-weight: 700; }
        .playfair { font-family: 'Playfair Display', serif; font-weight: 400; }
        .playfair-medium { font-family: 'Playfair Display', serif; font-weight: 500; }
        .playfair-semibold { font-family: 'Playfair Display', serif; font-weight: 600; }
        .playfair-bold { font-family: 'Playfair Display', serif; font-weight: 700; }
        .zi-xiaohun-gouyu { font-family: 'ZiXiaoHunGouYu', 'Dancing Script', cursive; }
        .zi-xiaohun-sanfen { font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif; }
        .zi-hun-xingyun { font-family: 'ZiHunXingYunFeiBai', 'Dancing Script', cursive; }

        /* 中英文分行显示样式 */
        .english-line {
            color: #40e0d0;
            margin-bottom: 6px;
            display: block;
        }

        .chinese-line {
            color: #40e0d0;
            margin-top: 6px;
            display: block;
        }

        /* 分行示例容器 */
        .bilingual-sample {
            padding: 8px 12px;
            background: #333333;
            border-radius: 3px;
            border-left: 3px solid #40e0d0;
            margin-bottom: 12px;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 { font-size: 1.6rem; }
            .font-grid { grid-template-columns: 1fr; }
            .stats { gap: 8px; }
            .stat-item { padding: 4px 8px; }
            .font-card { padding: 8px; }
        }

        @media (max-width: 1200px) {
            .font-grid { grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); }
        }

        .footer {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 4px;
            border: 1px solid #40e0d0;
        }

        .footer p {
            opacity: 0.8;
            margin-bottom: 5px;
            font-size: 0.85rem;
            color: #40e0d0;
        }
    </style>
    
    <!-- 导入字体样式 -->
    <link rel="stylesheet" href="/src/client/styles/style.css">
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>✨ Love Project 星空字体展示 (21个字体文件) - v2.0</h1>
            <p>星空主题 · 淡青色调 · 大间隔布局 · 分行显示 · woff2优化 · 更新: 2025-08-03</p>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">21</div>
                    <div class="stat-label">字体文件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">9</div>
                    <div class="stat-label">字体族</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">woff2</div>
                    <div class="stat-label">优化格式</div>
                </div>
            </div>
        </div>

        <!-- 字体展示网格 -->
        <div class="font-grid">
            <!-- 1. Courgette Regular -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Courgette Regular</div>
                    <div class="font-info">35.46 KB<br>装饰字体</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text courgette" style="font-size: 20px;">Love is in the air 爱情如诗如画</div>
                    <div class="sample-text courgette" style="font-size: 16px;">Beautiful moments together 美好时光相伴</div>
                </div>
            </div>

            <!-- 2. GreatVibes Regular -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Great Vibes Regular</div>
                    <div class="font-info">154.57 KB<br>手写字体</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text great-vibes" style="font-size: 22px;">Dancing through life with you 与你共舞人生</div>
                    <div class="sample-text great-vibes" style="font-size: 18px;">Every moment is a treasure 每一刻都是珍宝</div>
                </div>
            </div>

            <!-- 3. Dancing Script Regular -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Dancing Script Regular</div>
                    <div class="font-info">74.55 KB<br>手写字体</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text dancing-script" style="font-size: 20px;">Love grows more beautiful 爱情愈发美丽</div>
                    <div class="sample-text dancing-script" style="font-size: 16px;">Forever and always 永远永远</div>
                </div>
            </div>

            <!-- 4. Dancing Script Bold -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Dancing Script Bold</div>
                    <div class="font-info">74.8 KB<br>手写字体粗体</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text dancing-script-bold" style="font-size: 20px;">Bold love story 浓情爱意</div>
                    <div class="sample-text dancing-script-bold" style="font-size: 16px;">Strong together 携手同行</div>
                </div>
            </div>

            <!-- 5. Poppins Light -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Poppins Light</div>
                    <div class="font-info">152.53 KB<br>现代无衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text poppins-light" style="font-size: 18px;">Light and elegant design 轻盈优雅的设计</div>
                    <div class="sample-text poppins-light" style="font-size: 14px;">Modern typography 现代字体排版</div>
                </div>
            </div>

            <!-- 6. Poppins Regular -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Poppins Regular</div>
                    <div class="font-info">151 KB<br>现代无衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text poppins" style="font-size: 18px;">Professional and clean 专业简洁</div>
                    <div class="sample-text poppins" style="font-size: 14px;">Perfect for interfaces 界面设计首选</div>
                </div>
            </div>

            <!-- 7. Poppins Medium -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Poppins Medium</div>
                    <div class="font-info">149.28 KB<br>现代无衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text poppins-medium" style="font-size: 18px;">Medium weight balance 中等字重平衡</div>
                    <div class="sample-text poppins-medium" style="font-size: 14px;">Versatile and modern 多功能现代</div>
                </div>
            </div>

            <!-- 8. Poppins SemiBold -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Poppins SemiBold</div>
                    <div class="font-info">147.96 KB<br>现代无衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text poppins-semibold" style="font-size: 18px;">Strong presence 强烈存在感</div>
                    <div class="sample-text poppins-semibold" style="font-size: 14px;">Bold yet readable 粗体但易读</div>
                </div>
            </div>

            <!-- 9. Poppins Bold -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Poppins Bold</div>
                    <div class="font-info">146.77 KB<br>现代无衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text poppins-bold" style="font-size: 18px;">Bold and confident 粗体自信</div>
                    <div class="sample-text poppins-bold" style="font-size: 14px;">Maximum impact 最大冲击力</div>
                </div>
            </div>

            <!-- 10. Inter Light -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Inter Light</div>
                    <div class="font-info">318.11 KB<br>专业可读</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text inter-light" style="font-size: 16px;">Designed for interfaces 为界面而设计</div>
                    <div class="sample-text inter-light" style="font-size: 14px;">Excellent readability 出色可读性</div>
                </div>
            </div>

            <!-- 11. Inter Regular -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Inter Regular</div>
                    <div class="font-info">317.21 KB<br>专业可读</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text inter" style="font-size: 16px;">Perfect for body text 正文首选</div>
                    <div class="sample-text inter" style="font-size: 14px;">Highly legible 高度易读</div>
                </div>
            </div>

            <!-- 12. Inter Medium -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Inter Medium</div>
                    <div class="font-info">317.68 KB<br>专业可读</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text inter-medium" style="font-size: 16px;">Balanced weight 平衡字重</div>
                    <div class="sample-text inter-medium" style="font-size: 14px;">Clear hierarchy 清晰层次</div>
                </div>
            </div>

            <!-- 13. Inter SemiBold -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Inter SemiBold</div>
                    <div class="font-info">318.41 KB<br>专业可读</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text inter-semibold" style="font-size: 16px;">Strong emphasis 强烈强调</div>
                    <div class="sample-text inter-semibold" style="font-size: 14px;">Clear distinction 清晰区分</div>
                </div>
            </div>

            <!-- 14. Inter Bold -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Inter Bold</div>
                    <div class="font-info">318.81 KB<br>专业可读</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text inter-bold" style="font-size: 16px;">Maximum impact 最大冲击</div>
                    <div class="sample-text inter-bold" style="font-size: 14px;">Bold statements 粗体声明</div>
                </div>
            </div>

            <!-- 15. Playfair Display Regular -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Playfair Display Regular</div>
                    <div class="font-info">120.35 KB<br>优雅衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text playfair" style="font-size: 18px;">Elegant serif design 优雅衬线设计</div>
                    <div class="sample-text playfair" style="font-size: 14px;">Classic beauty 经典美感</div>
                </div>
            </div>

            <!-- 16. Playfair Display Medium -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Playfair Display Medium</div>
                    <div class="font-info">120.71 KB<br>优雅衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text playfair-medium" style="font-size: 18px;">Medium elegance 中等优雅</div>
                    <div class="sample-text playfair-medium" style="font-size: 14px;">Refined typography 精致排版</div>
                </div>
            </div>

            <!-- 17. Playfair Display SemiBold -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Playfair Display SemiBold</div>
                    <div class="font-info">120.77 KB<br>优雅衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text playfair-semibold" style="font-size: 18px;">Strong elegance 强烈优雅</div>
                    <div class="sample-text playfair-semibold" style="font-size: 14px;">Bold sophistication 粗体精致</div>
                </div>
            </div>

            <!-- 18. Playfair Display Bold -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">Playfair Display Bold</div>
                    <div class="font-info">120.64 KB<br>优雅衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-text playfair-bold" style="font-size: 18px;">Bold elegance 粗体优雅</div>
                    <div class="sample-text playfair-bold" style="font-size: 14px;">Maximum sophistication 最大精致度</div>
                </div>
            </div>

            <!-- 19. 字小魂勾玉行书 -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">字小魂勾玉行书</div>
                    <div class="font-info">5.5 MB<br>中文行书</div>
                </div>
                <div class="font-samples">
                    <div class="bilingual-sample zi-xiaohun-gouyu" style="font-size: 20px;">
                        <div class="english-line">Love is the poetry of the senses</div>
                        <div class="chinese-line">爱情如诗如画，岁月静好如初</div>
                    </div>
                    <div class="bilingual-sample zi-xiaohun-gouyu" style="font-size: 16px;">
                        <div class="english-line">Beautiful moments together forever</div>
                        <div class="chinese-line">山有木兮木有枝，心悦君兮君不知</div>
                    </div>
                </div>
            </div>

            <!-- 20. 字小魂三分行楷 -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">字小魂三分行楷</div>
                    <div class="font-info">8.88 MB<br>中文行楷</div>
                </div>
                <div class="font-samples">
                    <div class="bilingual-sample zi-xiaohun-sanfen" style="font-size: 20px;">
                        <div class="english-line">Together we create beautiful memories</div>
                        <div class="chinese-line">相遇是缘分，相守是幸福</div>
                    </div>
                    <div class="bilingual-sample zi-xiaohun-sanfen" style="font-size: 16px;">
                        <div class="english-line">Time flows like water, love remains eternal</div>
                        <div class="chinese-line">时光荏苒，唯有真情永恒</div>
                    </div>
                </div>
            </div>

            <!-- 21. 字魂行云飞白体 -->
            <div class="font-card">
                <div class="font-header">
                    <div class="font-name">字魂行云飞白体</div>
                    <div class="font-info">5.25 MB<br>中文艺术</div>
                </div>
                <div class="font-samples">
                    <div class="bilingual-sample zi-hun-xingyun" style="font-size: 22px;">
                        <div class="english-line">Forever and always, my love</div>
                        <div class="chinese-line">执子之手，与子偕老</div>
                    </div>
                    <div class="bilingual-sample zi-hun-xingyun" style="font-size: 18px;">
                        <div class="english-line">Clouds drift and flowers bloom</div>
                        <div class="chinese-line">云卷云舒，花开花落</div>
                    </div>
                </div>
            </div>

        </div>

        <!-- 页面底部 -->
        <div class="footer">
            <p>✨ Love Project 星空字体系统 - 21个字体文件完整展示 (v2.0)</p>
            <p style="margin-top: 10px; font-size: 0.9rem; opacity: 0.7;">
                星空主题 · 淡青色调 · 大间隔布局 · 分行显示 · woff2优化 · 智能加载 · 更新: 2025-08-03
            </p>
        </div>
    </div>

    <script>
        // 简单的页面加载完成提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Love Project 字体展示页面加载完成 - 21个字体文件');
        });
    </script>
</body>
</html>
