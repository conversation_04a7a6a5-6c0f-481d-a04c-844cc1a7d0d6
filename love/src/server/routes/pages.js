/**
 * 页面路由
 * 处理静态页面和资源的路由
 */

const express = require('express');
const path = require('path');
const router = express.Router();

// 静态文件服务配置
const staticOptions = {
    maxAge: '1d', // 缓存1天
    etag: true,
    lastModified: true
};

// 主页路由
router.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/index.html'));
});

// 在一起的日子页面
router.get('/together-days', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/together-days.html'));
});

// 纪念日页面
router.get('/anniversary', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/anniversary.html'));
});

// 相遇记录页面
router.get('/meetings', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/meetings.html'));
});

// 纪念页面
router.get('/memorial', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/memorial.html'));
});

// 测试验证页面
router.get('/verify', (req, res) => {
    res.sendFile(path.join(__dirname, '../../../test/test-api.html'));
});

// VPS本地服务配置 - 第三层视频保障
router.use('/video/compressed', express.static(path.join(__dirname, '../../client/assets/video-compressed'), {
    ...staticOptions,
    maxAge: '1y', // 视频文件缓存1年
    setHeaders: (res, path) => {
        // 设置CORS头，允许跨域访问
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET');
        res.setHeader('Access-Control-Allow-Headers', 'Range');

        // 设置正确的MIME类型
        if (path.endsWith('.mp4')) {
            res.setHeader('Content-Type', 'video/mp4');
        }

        // 支持Range请求，用于视频流播放
        res.setHeader('Accept-Ranges', 'bytes');

        // 设置缓存控制
        res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
    }
}));

// 测试文件路由 - 禁用缓存以便实时更新
const testStaticOptions = {
    maxAge: 0, // 禁用缓存
    etag: false,
    lastModified: true,
    setHeaders: (res, path) => {
        // 强制禁用缓存
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
    }
};

router.use('/test', express.static(path.join(__dirname, '../../../test'), testStaticOptions));
router.use('/background', express.static(path.join(__dirname, '../../../background'), staticOptions));
router.use('/fonts', express.static(path.join(__dirname, '../../../fonts'), staticOptions));

// 主样式文件路由
router.get('/main.css', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/styles/main.css'));
});

// 健康检查页面
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Love site is running',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

module.exports = router;
