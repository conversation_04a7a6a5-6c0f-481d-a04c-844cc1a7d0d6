<!DOCTYPE html>
<html lang="zh-CN" style="background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%) !important; margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="记录我们美好爱情故事的专属网站，包含恋爱天数计算器和生日倒计时">
    <meta name="keywords" content="爱情, 情侣, 纪念日, 生日, 恋爱天数">
    <title>Yu 💕 Wang</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💕</text></svg>">

    <!-- 立即应用背景样式，防止白色闪烁 -->
    <style>
        html {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%) !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }
        body {
            background: transparent !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }

        /* 立即显示加载遮罩，防止看到背景 */
        .loading-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%) !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            align-items: center !important;
            z-index: 9999 !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 使用CSS立即显示加载动画 */
        .loading-overlay::before {
            content: '' !important;
            width: 60px !important;
            height: 60px !important;
            border: 4px solid rgba(255, 255, 255, 0.3) !important;
            border-top: 4px solid white !important;
            border-radius: 50% !important;
            animation: spin 1s linear infinite !important;
            margin-bottom: 20px !important;
            display: block !important;
        }

        /* 使用CSS立即显示加载文字 */
        .loading-overlay::after {
            content: '💕 Loading Home...' !important;
            white-space: pre-line !important;
            color: white !important;
            font-family: 'Dancing Script', 'Great Vibes', 'Courgette', cursive !important;
            font-size: 2.5rem !important;
            font-weight: 700 !important;
            text-align: center !important;
            line-height: 1.6 !important;
            display: block !important;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
            letter-spacing: 2px !important;
        }

        .loading-overlay.hidden {
            opacity: 0 !important;
            visibility: hidden !important;
            transition: opacity 0.8s ease-out, visibility 0.8s ease-out !important;
        }

        /* 加载动画样式 */
        .loading-spinner {
            width: 60px !important;
            height: 60px !important;
            border: 4px solid rgba(255, 255, 255, 0.3) !important;
            border-top: 4px solid white !important;
            border-radius: 50% !important;
            animation: spin 1s linear infinite !important;
            margin-bottom: 20px !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: white !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
            font-size: 1.5rem !important;
            font-weight: 600 !important;
            text-align: center !important;
            margin-bottom: 10px !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }

        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8) !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
            font-size: 1rem !important;
            text-align: center !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }

        /* 加载进度条 */
        .loading-progress {
            width: 200px !important;
            height: 4px !important;
            background: rgba(255, 255, 255, 0.3) !important;
            border-radius: 2px !important;
            margin-top: 20px !important;
            overflow: hidden !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }

        .loading-progress-bar {
            height: 100% !important;
            background: white !important;
            border-radius: 2px !important;
            width: 0% !important;
            transition: width 0.3s ease !important;
            animation: progressPulse 2s ease-in-out infinite !important;
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }

        @keyframes progressPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>


    <!-- 时间戳打印脚本 -->
    <script>
        // 记录页面开始加载时间
        const pageStartTime = performance.now();

        // 使用多种方式确保调试信息可见
        console.clear(); // 清理之前的日志
        console.log('🚀 页面开始加载时间:', new Date().toISOString(), '相对时间:', pageStartTime.toFixed(2) + 'ms');
        console.log('🔄 这是F5刷新还是首次加载？检查performance.navigation.type:', performance.navigation ? performance.navigation.type : 'unknown');

        // 在页面标题中也显示加载状态（用于调试）
        document.title = '🚀 Loading... - Yu 💕 Wang';

        // 检查HTML根元素背景
        console.log('🎨 HTML根元素背景已设置:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
        console.log('🎨 HTML根元素背景色:', document.documentElement.style.background || '内联样式');

        // 立即检查当前页面背景状态
        setTimeout(() => {
            const htmlBg = window.getComputedStyle(document.documentElement).background;
            const bodyBg = window.getComputedStyle(document.body).background;
            console.log('🎨 计算后HTML背景:', htmlBg.substring(0, 100) + '...');
            console.log('🎨 计算后Body背景:', bodyBg.substring(0, 100) + '...');
        }, 0);

        // 立即打印遮罩显示时间（因为遮罩和内容都是通过CSS立即显示的）
        console.log('🎭 遮罩背景显示时间:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
        console.log('⭕ 加载动画显示时间(CSS):', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
        console.log('📝 加载文字显示时间(CSS):', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');

        // 监听DOM内容加载完成
        document.addEventListener('DOMContentLoaded', function() {
            const domLoadTime = performance.now();
            console.log('📄 DOM加载完成时间:', new Date().toISOString(), '相对时间:', domLoadTime.toFixed(2) + 'ms');

            // 恢复正常标题
            document.title = 'Yu 💕 Wang';

            // 分析页面加载类型
            if (performance.navigation) {
                const navType = performance.navigation.type;
                const navTypes = {
                    0: '正常导航（链接、地址栏、书签等）',
                    1: 'F5刷新或重新加载',
                    2: '前进/后退按钮',
                    255: '其他方式'
                };
                console.log('🔄 页面加载类型:', navTypes[navType] || '未知(' + navType + ')');

                if (navType === 1) {
                    console.log('🔄 这是F5刷新！分析刷新时的背景闪烁问题...');

                    // F5刷新时的特殊背景分析
                    setTimeout(() => {
                        console.log('🔍 F5刷新背景分析:');

                        // 检查所有可能显示的背景层
                        const htmlBg = window.getComputedStyle(document.documentElement).background;
                        const bodyBg = window.getComputedStyle(document.body).background;

                        console.log('🎨 F5刷新时HTML背景:', htmlBg.includes('linear-gradient') ? '粉色渐变✅' : htmlBg);
                        console.log('🎨 F5刷新时Body背景:', bodyBg.includes('transparent') ? '透明✅' : bodyBg);

                        const videoContainer = document.querySelector('.video-background');
                        if (videoContainer) {
                            const containerBg = window.getComputedStyle(videoContainer).background;
                            const beforeStyle = window.getComputedStyle(videoContainer, '::before');

                            console.log('🎬 F5刷新时视频容器背景:', containerBg.substring(0, 50) + '...');
                            console.log('🎬 F5刷新时::before背景:', beforeStyle.background.includes('linear-gradient') ? '粉色渐变✅' : beforeStyle.background.substring(0, 50) + '...');
                            console.log('🎬 F5刷新时::before透明度:', beforeStyle.opacity);

                            // 比较背景颜色是否一致
                            if (htmlBg.includes('#fce7f3') && beforeStyle.background.includes('#fce7f3')) {
                                console.log('✅ HTML和::before背景颜色一致');
                            } else {
                                console.log('❌ HTML和::before背景颜色不一致！这可能是闪烁的原因');
                            }
                        }
                    }, 100);
                }
            }

            // 详细检查所有背景元素
            console.log('🔍 DOM加载完成后的背景检查:');

            // 检查HTML根元素
            const htmlStyle = window.getComputedStyle(document.documentElement);
            console.log('🎨 HTML根元素背景:', htmlStyle.background.substring(0, 100) + '...');

            // 检查Body元素
            const bodyStyle = window.getComputedStyle(document.body);
            console.log('🎨 Body元素背景:', bodyStyle.background.substring(0, 100) + '...');

            // 检查视频背景容器
            const videoContainer = document.querySelector('.video-background');
            if (videoContainer) {
                const containerStyle = window.getComputedStyle(videoContainer);
                const beforeStyle = window.getComputedStyle(videoContainer, '::before');
                console.log('🎬 视频容器背景:', containerStyle.background.substring(0, 50) + '...');
                console.log('🎬 视频容器::before背景:', beforeStyle.background.substring(0, 50) + '...');
                console.log('🎬 视频容器::before透明度:', beforeStyle.opacity);
                console.log('🎬 视频容器::before z-index:', beforeStyle.zIndex);
            }

            // 检查加载遮罩
            let loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                const overlayStyle = window.getComputedStyle(loadingOverlay);
                console.log('🎭 加载遮罩背景:', overlayStyle.background.substring(0, 50) + '...');
                console.log('🎭 加载遮罩透明度:', overlayStyle.opacity);
                console.log('🎭 加载遮罩z-index:', overlayStyle.zIndex);
            }

            // 检查加载卡片元素是否存在并可见
            const loadingSpinner = document.querySelector('.loading-spinner');
            const loadingText = document.querySelector('.loading-text');
            const loadingSubtitle = document.querySelector('.loading-subtitle');
            const loadingProgress = document.querySelector('.loading-progress');

            if (loadingOverlay) {
                const overlayStyle = window.getComputedStyle(loadingOverlay);
                console.log('🎭 遮罩元素状态:', {
                    display: overlayStyle.display,
                    opacity: overlayStyle.opacity,
                    visibility: overlayStyle.visibility,
                    时间: new Date().toISOString(),
                    相对时间: performance.now().toFixed(2) + 'ms'
                });
            }

            if (loadingSpinner) {
                const spinnerStyle = window.getComputedStyle(loadingSpinner);
                console.log('⭕ 加载动画状态:', {
                    display: spinnerStyle.display,
                    opacity: spinnerStyle.opacity,
                    visibility: spinnerStyle.visibility,
                    时间: new Date().toISOString(),
                    相对时间: performance.now().toFixed(2) + 'ms'
                });
            }

            if (loadingText) {
                const textStyle = window.getComputedStyle(loadingText);
                console.log('📝 加载文字状态:', {
                    display: textStyle.display,
                    opacity: textStyle.opacity,
                    visibility: textStyle.visibility,
                    内容: loadingText.textContent,
                    时间: new Date().toISOString(),
                    相对时间: performance.now().toFixed(2) + 'ms'
                });
            }

            if (loadingSubtitle) {
                const subtitleStyle = window.getComputedStyle(loadingSubtitle);
                console.log('📋 副标题状态:', {
                    display: subtitleStyle.display,
                    opacity: subtitleStyle.opacity,
                    visibility: subtitleStyle.visibility,
                    内容: loadingSubtitle.textContent,
                    时间: new Date().toISOString(),
                    相对时间: performance.now().toFixed(2) + 'ms'
                });
            }

            if (loadingProgress) {
                const progressStyle = window.getComputedStyle(loadingProgress);
                console.log('📊 进度条状态:', {
                    display: progressStyle.display,
                    opacity: progressStyle.opacity,
                    visibility: progressStyle.visibility,
                    时间: new Date().toISOString(),
                    相对时间: performance.now().toFixed(2) + 'ms'
                });
            }
        });

        // 监听页面完全加载
        window.addEventListener('load', function() {
            const pageLoadTime = performance.now();
            console.log('🏁 页面完全加载时间:', new Date().toISOString(), '相对时间:', pageLoadTime.toFixed(2) + 'ms');
            console.log('⏱️ 总加载时间:', (pageLoadTime - pageStartTime).toFixed(2) + 'ms');

            // 页面完全加载后的最终背景状态检查
            setTimeout(() => {
                console.log('🔍 页面完全加载后的最终背景状态检查:');

                const htmlStyle = window.getComputedStyle(document.documentElement);
                const bodyStyle = window.getComputedStyle(document.body);
                console.log('🎨 最终HTML背景:', htmlStyle.background.substring(0, 50) + '...');
                console.log('🎨 最终Body背景:', bodyStyle.background.substring(0, 50) + '...');

                const videoContainer = document.querySelector('.video-background');
                if (videoContainer) {
                    const containerStyle = window.getComputedStyle(videoContainer);
                    const beforeStyle = window.getComputedStyle(videoContainer, '::before');
                    console.log('🎬 最终视频容器背景:', containerStyle.background.substring(0, 50) + '...');
                    console.log('🎬 最终视频容器::before背景:', beforeStyle.background.substring(0, 50) + '...');
                    console.log('🎬 最终视频容器::before透明度:', beforeStyle.opacity);
                    console.log('🎬 最终视频容器是否有video-loaded类:', videoContainer.classList.contains('video-loaded'));
                }

                if (loadingOverlay) {
                    const overlayStyle = window.getComputedStyle(loadingOverlay);
                    console.log('🎭 最终加载遮罩透明度:', overlayStyle.opacity);
                    console.log('🎭 最终加载遮罩可见性:', overlayStyle.visibility);
                }
            }, 500);
        });
    </script>

    <script>
        console.log('🎨 准备加载外部CSS:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
    </script>

    <link rel="stylesheet" href="/src/client/styles/style.css?v=green-buttons-020">
    <!-- Google Fonts CDN已移除，使用本地R2+VPS双源字体加载 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 自定义样式：让留言时间字体与用户名保持一致 + 留言板标题闪光效果 + 折叠按钮透明背景 -->
    <style>
        .message-date {
            font-family: 'Dancing Script', cursive !important;
            font-weight: 700 !important;
        }
        
        /* 留言板标题闪闪发光效果 */
        .messages-header h3 {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
        }
        
        @keyframes sparkle {
            0%, 100% {
                background-position: 0% 50%;
                filter: brightness(1);
            }
            25% {
                background-position: 25% 50%;
                filter: brightness(1.2);
            }
            50% {
                background-position: 100% 50%;
                filter: brightness(1.5);
            }
            75% {
                background-position: 75% 50%;
                filter: brightness(1.2);
            }
        }
        
        /* 折叠按钮透明背景 */
        .collapse-btn {
            background: transparent !important;
            border: 2px solid rgba(103, 232, 249, 0.6) !important;
        }
        
        .collapse-btn:hover {
            background: rgba(103, 232, 249, 0.1) !important;
            border-color: #67e8f9 !important;
            color: #67e8f9 !important;
        }
        
        /* 增大留言内容的字体 */
        .message-content {
            font-size: 1.4rem !important;
        }
        
        /* 增大输入框的字体和占位符文字，确保输入文字颜色与留言栏一致 */
        #messageText {
            font-size: 1.4rem !important;
            color: #d4af37 !important; /* 淡金色，与留言栏文字颜色一致 */
        }
        
        /* 确保输入框中输入的文字颜色与留言显示颜色完全一致 */
        #messageText,
        #messageText:focus,
        #messageText:active,
        textarea#messageText {
            color: #d4af37 !important; /* 淡金色，强制覆盖所有其他颜色设置 */
            caret-color: #d4af37 !important; /* 光标颜色也设为淡金色 */
        }
        
        #messageText::placeholder {
            font-size: 1.4rem !important;
        }
        
        /* 修复倾斜效果：默认不倾斜，悬停时才倾斜 */
        .counter-card h2 {
            transform: rotate(0deg) !important;
        }
        
        .counter-card h2:hover {
            transform: rotate(-1deg) scale(1.02) !important;
        }
        
        .quote-text:not(.romantic-quote-content .quote-text) {
            transform: rotate(0deg) !important;
        }
        
        .quote-text:not(.romantic-quote-content .quote-text):hover {
            transform: rotate(-1deg) scale(1.02) !important;
        }
        
        /* "写下爱的话语"闪闪发光效果 */
        .form-header h3 {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
        }
        
        /* 底部四个回忆板块不同颜色闪闪发光效果 */
        
        /* 在一起的日子 - 蓝紫色 */
        .memory-item:nth-child(1) .memory-placeholder p {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6, #8b5cf6) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3) !important;
        }
        
        .memory-item:nth-child(1) .memory-placeholder i {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6, #8b5cf6) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3) !important;
        }
        
        /* 纪念日 - 金橙色 */
        .memory-item:nth-child(2) .memory-placeholder p {
            background: linear-gradient(45deg, #f59e0b, #f97316, #f59e0b, #f97316) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(245, 158, 11, 0.5), 0 0 20px rgba(249, 115, 22, 0.3) !important;
        }
        
        .memory-item:nth-child(2) .memory-placeholder i {
            background: linear-gradient(45deg, #f59e0b, #f97316, #f59e0b, #f97316) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(245, 158, 11, 0.5), 0 0 20px rgba(249, 115, 22, 0.3) !important;
        }
        
        /* 纪念物 - 绿青色 */
        .memory-item:nth-child(3) .memory-placeholder p {
            background: linear-gradient(45deg, #10b981, #06b6d4, #10b981, #06b6d4) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(16, 185, 129, 0.5), 0 0 20px rgba(6, 182, 212, 0.3) !important;
        }
        
        .memory-item:nth-child(3) .memory-placeholder i {
            background: linear-gradient(45deg, #10b981, #06b6d4, #10b981, #06b6d4) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(16, 185, 129, 0.5), 0 0 20px rgba(6, 182, 212, 0.3) !important;
        }
        
        /* 每一次相遇 - 红粉色 */
        .memory-item:nth-child(4) .memory-placeholder p {
            background: linear-gradient(45deg, #ef4444, #ec4899, #ef4444, #ec4899) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(239, 68, 68, 0.5), 0 0 20px rgba(236, 72, 153, 0.3) !important;
        }
        
                 .memory-item:nth-child(4) .memory-placeholder i {
             background: linear-gradient(45deg, #ef4444, #ec4899, #ef4444, #ec4899) !important;
             background-size: 400% 400% !important;
             -webkit-background-clip: text !important;
             -webkit-text-fill-color: transparent !important;
             background-clip: text !important;
             animation: sparkle 2s ease-in-out infinite !important;
             text-shadow: 0 0 10px rgba(239, 68, 68, 0.5), 0 0 20px rgba(236, 72, 153, 0.3) !important;
         }
         
         /* 底部版权文字闪闪发光效果 - 浪漫红粉色 */
         .footer p {
             background: linear-gradient(45deg, #e91e63, #ff6b9d, #e91e63, #ff6b9d) !important;
             background-size: 400% 400% !important;
             -webkit-background-clip: text !important;
             -webkit-text-fill-color: transparent !important;
             background-clip: text !important;
             animation: sparkle 2s ease-in-out infinite !important;
             text-shadow: 0 0 10px rgba(233, 30, 99, 0.5), 0 0 20px rgba(255, 107, 157, 0.3) !important;
         }
         
                   .footer p i {
              background: linear-gradient(45deg, #e91e63, #ff6b9d, #e91e63, #ff6b9d) !important;
              background-size: 400% 400% !important;
              -webkit-background-clip: text !important;
              -webkit-text-fill-color: transparent !important;
              background-clip: text !important;
              animation: sparkle 2s ease-in-out infinite !important;
              text-shadow: 0 0 10px rgba(233, 30, 99, 0.5), 0 0 20px rgba(255, 107, 157, 0.3) !important;
          }
          
                     /* 美化情话板块 - 透明背景 */
           .quote-card {
               position: relative !important;
               background: transparent !important;
               border: 2px solid rgba(255, 255, 255, 0.3) !important;
               border-radius: 25px !important;
               padding: 35px !important;
               box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 
                          0 0 25px rgba(255, 107, 157, 0.15) !important;
               overflow: hidden !important;
           }
          
          .quote-card::before {
              content: '' !important;
              position: absolute !important;
              top: -50% !important;
              left: -50% !important;
              width: 200% !important;
              height: 200% !important;
              background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%) !important;
              animation: shimmer 4s linear infinite !important;
              pointer-events: none !important;
          }
          
                     /* 右上角换一句按钮 - 透明背景 */
           .quote-btn-top-right {
               position: absolute !important;
               top: 15px !important;
               right: 15px !important;
               background: transparent !important;
               border: 2px solid rgba(103, 232, 249, 0.6) !important;
               border-radius: 20px !important;
               padding: 8px 16px !important;
               font-size: 0.9rem !important;
               color: #67e8f9 !important;
               transition: all 0.3s ease !important;
               z-index: 10 !important;
           }
           
           .quote-btn-top-right:hover {
               background: rgba(103, 232, 249, 0.1) !important;
               border-color: #22d3ee !important;
               color: #22d3ee !important;
               transform: translateY(-2px) scale(1.05) !important;
               box-shadow: 0 8px 25px rgba(103, 232, 249, 0.3) !important;
           }
          
          /* 美化引用图标 */
          .quote-icon {
              color: #67e8f9 !important;
              font-size: 1.8rem !important;
              margin-bottom: 20px !important;
              display: block !important;
              text-shadow: 0 0 15px rgba(103, 232, 249, 0.5) !important;
          }
    </style>
</head>
<body>
    <!-- 加载遮罩 - 纯CSS显示 -->
    <div class="loading-overlay" id="loadingOverlay"></div></body>

    <!-- 花朵视频背景 -->
    <div class="video-background">
        <video autoplay muted loop playsinline preload="metadata">
            <source src="/src/client/assets/video-compressed/home.mp4" type="video/mp4">
            <!-- 如果浏览器不支持视频，显示备用背景 -->
            您的浏览器不支持视频播放。
        </video>
    </div>

    <script>
        console.log('🎬 视频背景元素已创建:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');

        // 检查视频背景元素的样式
        setTimeout(() => {
            const videoContainer = document.querySelector('.video-background');
            if (videoContainer) {
                const containerStyle = window.getComputedStyle(videoContainer);
                const beforeStyle = window.getComputedStyle(videoContainer, '::before');
                console.log('🎬 视频容器背景:', containerStyle.background.substring(0, 50) + '...');
                console.log('🎬 视频容器::before背景:', beforeStyle.background.substring(0, 50) + '...');
                console.log('🎬 视频容器::before透明度:', beforeStyle.opacity);
            }
        }, 0);
    </script>

    <div class="container">
        <!-- 可点击的浪漫星星 -->
        <div class="romantic-stars">
            <div class="romantic-star star-variant-1" data-quote-category="confession" style="top: 15%; left: 20%;"></div>
            <div class="romantic-star star-variant-2" data-quote-category="sweet" style="top: 25%; left: 80%;"></div>
            <div class="romantic-star star-variant-3" data-quote-category="promise" style="top: 35%; left: 15%;"></div>
            <div class="romantic-star star-variant-4" data-quote-category="missing" style="top: 45%; left: 85%;"></div>
            <div class="romantic-star star-variant-5" data-quote-category="confession" style="top: 55%; left: 25%;"></div>
            <div class="romantic-star star-variant-6" data-quote-category="sweet" style="top: 65%; left: 75%;"></div>
            <div class="romantic-star star-variant-7" data-quote-category="promise" style="top: 75%; left: 10%;"></div>
            <div class="romantic-star star-variant-8" data-quote-category="missing" style="top: 85%; left: 90%;"></div>
            <!-- 新增更多星星，使用新的颜色变体 -->
            <div class="romantic-star star-variant-9" data-quote-category="confession" style="top: 12%; left: 60%;"></div>
            <div class="romantic-star star-variant-10" data-quote-category="sweet" style="top: 28%; left: 45%;"></div>
            <div class="romantic-star star-variant-11" data-quote-category="promise" style="top: 42%; left: 30%;"></div>
            <div class="romantic-star star-variant-12" data-quote-category="missing" style="top: 58%; left: 70%;"></div>
            <div class="romantic-star star-variant-13" data-quote-category="confession" style="top: 72%; left: 55%;"></div>
            <div class="romantic-star star-variant-14" data-quote-category="sweet" style="top: 88%; left: 35%;"></div>
            <div class="romantic-star star-variant-15" data-quote-category="promise" style="top: 18%; left: 95%;"></div>
            <div class="romantic-star star-variant-16" data-quote-category="missing" style="top: 38%; left: 5%;"></div>
        </div>

        <!-- 浪漫话语模态框 -->
        <div id="romanticModal" class="romantic-modal">
            <div class="romantic-modal-content">
                <div class="romantic-modal-header">
                    <h3>💫 星星的话语</h3>
                    <button class="close-romantic-btn" onclick="closeRomanticModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="romantic-quote-content">
                    <p class="quote-text" id="romanticQuoteText"></p>
                    <div class="quote-attribution">
                        <span class="quote-author" id="romanticQuoteAuthor"></span>
                        <span class="quote-work" id="romanticQuoteWork"></span>
                    </div>
                </div>
                <div class="romantic-modal-footer">
                    <button class="new-quote-btn" onclick="getNewQuote()">
                        <i class="fas fa-sync-alt"></i>
                        换一句
                    </button>
                </div>
            </div>
        </div>

        <!-- 流星效果 -->
        <div class="shooting-stars">
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
        </div>

        <!-- 浪漫漂浮元素 -->
        <div class="hearts-container">
            <div class="heart type-1 small"></div>
            <div class="heart type-2 medium"></div>
            <div class="heart type-3 large"></div>
            <div class="heart type-4 small"></div>
            <div class="heart type-5 medium"></div>
            <div class="heart type-6 large"></div>
            <div class="heart type-7 small"></div>
            <div class="heart type-8 medium"></div>
            <div class="heart type-9 large"></div>
            <div class="heart type-10 small"></div>
            <div class="heart type-11 medium"></div>
            <div class="heart type-12 large"></div>
            <div class="heart type-1 medium"></div>
            <div class="heart type-3 small"></div>
            <div class="heart type-5 large"></div>
        </div>

        <!-- Main content -->
        <header class="header">
            <h1 class="title">
                Yu
                <i class="fas fa-heart"></i>
                Wang
            </h1>
            <p class="subtitle">Forever and Always 💕</p>
        </header>

        <main class="main-content">
            <!-- Love counter section -->
            <section class="love-counter">
                <div class="counter-card">
                    <h2>我们在一起已经</h2>
                    <div class="counter-display">
                        <div class="counter-item">
                            <span class="counter-number" id="days">0</span>
                            <span class="counter-label">天</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="hours">0</span>
                            <span class="counter-label">小时</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="minutes">0</span>
                            <span class="counter-label">分钟</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="seconds">0</span>
                            <span class="counter-label">秒</span>
                        </div>
                    </div>
                    <p class="start-date">从 2023年4月23日 开始 💖</p>
                </div>
            </section>

            <!-- Birthday section -->
            <section class="birthday-section">
                <div class="birthday-cards">
                    <div class="birthday-card boy">
                        <div class="birthday-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <h3><span class="name-yu">Yu</span><span class="birthday-text">的生日</span></h3>
                        <div class="birthday-date">01月16日</div>
                        <div class="birthday-countdown">
                            <span>距离下次生日还有</span>
                            <span class="countdown-days" id="boy-countdown">0</span>
                            <span>天</span>
                        </div>
                    </div>

                    <div class="birthday-card girl">
                        <div class="birthday-icon">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <h3><span class="name-wang">Wang</span><span class="birthday-text">的生日</span></h3>
                        <div class="birthday-date">04月15日</div>
                        <div class="birthday-countdown">
                            <span>距离下次生日还有</span>
                            <span class="countdown-days" id="girl-countdown">0</span>
                            <span>天</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Love quotes section -->
            <section class="love-quotes">
                <div class="quote-card">
                    <button class="quote-btn quote-btn-top-right" onclick="changeQuote()">
                        <i class="fas fa-sync-alt"></i>
                        换一句
                    </button>
                    <i class="fas fa-quote-left quote-icon"></i>
                    <p class="quote-text" id="quote-text">爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。</p>
                </div>
            </section>

            <!-- Love Messages section -->
            <section class="love-messages-section">
                <h2>我们的爱情记录</h2>

                <!-- Message input form -->
                <div class="message-input-card">
                    <div class="message-form">
                        <div class="form-header">
                            <i class="fas fa-pen-fancy"></i>
                            <h3>写下爱的话语</h3>
                        </div>

                        <div class="author-and-template-row">
                            <div class="message-templates">
                                <button class="template-btn" onclick="showTemplates()">
                                    <i class="fas fa-magic"></i>
                                    选择浪漫模板
                                </button>
                            </div>
                            <div class="author-selection">
                                <label class="author-option">
                                    <input type="radio" name="author" value="Yu" checked>
                                    <span class="author-label yu">
                                        <i class="fas fa-heart"></i>
                                        Yu
                                    </span>
                                </label>
                                <label class="author-option">
                                    <input type="radio" name="author" value="Wang">
                                    <span class="author-label wang">
                                        <i class="fas fa-heart"></i>
                                        Wang
                                    </span>
                                </label>
                                <label class="author-option">
                                    <input type="radio" name="author" value="Other">
                                    <span class="author-label other">
                                        <i class="fas fa-heart"></i>
                                        Other
                                    </span>
                                </label>
                            </div>
                        </div>

                        <textarea id="messageText" placeholder="选择一个用户，然后写下你想说的话..."></textarea>

                        <div class="form-actions">
                            <button class="save-btn" onclick="saveMessage()">
                                <i class="fas fa-heart"></i>
                                保存爱的留言
                            </button>
                            <button class="clear-btn" onclick="clearMessage()">
                                <i class="fas fa-eraser"></i>
                                清空
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Message templates modal -->
                <div id="templatesModal" class="templates-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>选择浪漫模板</h3>
                            <button class="close-btn" onclick="closeTemplates()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="templates-grid" id="templatesGrid">
                            <!-- Templates will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Messages display -->
                <div class="messages-container">
                    <div class="messages-header">
                        <h3>留言板</h3>
                        <div class="messages-actions">
                            <button class="collapse-btn" onclick="toggleMessagesCollapse()" title="折叠">
                                <i class="fas fa-chevron-down" id="collapseIcon"></i>
                            </button>
                            <button class="action-btn" onclick="exportMessages()">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                            <button class="action-btn" onclick="clearAllMessages()">
                                <i class="fas fa-trash"></i>
                                清空所有
                            </button>
                        </div>
                    </div>
                    <div class="messages-collapsible-content" id="messagesCollapsibleContent">
                        <div id="messagesList" class="messages-list">
                            <!-- Messages will be displayed here -->
                        </div>
                        <div id="emptyState" class="empty-state">
                            <i class="fas fa-heart-broken"></i>
                            <p>还没有留言，快来写下第一条爱的话语吧！</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Memory gallery -->
            <section class="memory-section">
                <h2>我们的美好回忆</h2>
                <div class="memory-grid">
                    <div class="memory-item" onclick="window.location.href = getPageUrl('TOGETHER_DAYS')">
                        <div class="memory-placeholder">
                            <i class="fas fa-calendar-alt"></i>
                            <p>在一起的日子</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href = getPageUrl('ANNIVERSARY')">
                        <div class="memory-placeholder">
                            <i class="fas fa-star"></i>
                            <p>纪念日</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href = getPageUrl('MEMORIAL')">
                        <div class="memory-placeholder">
                            <i class="fas fa-gift"></i>
                            <p>纪念物</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href = getPageUrl('MEETINGS')">
                        <div class="memory-placeholder">
                            <i class="fas fa-users"></i>
                            <p>每一次相遇</p>
                        </div>
                    </div>
                </div>
            </section>


        </main>

        <footer class="footer">
            <p>Made with <i class="fas fa-heart"></i> for our love story</p>
        </footer>
    </div>

    <!-- 引入前端配置 -->
    <script src="/src/client/scripts/config.js"></script>

    <!-- 智能视频加载器 - 四层CDN架构 -->
    <script src="/src/client/scripts/video-loader.js"></script>

    <!-- 智能字体加载器 - R2优先+本地降级架构 -->
    <script src="/src/client/assets/fonts/font-controller.js"></script>

    <script src="/src/client/scripts/romantic-quotes.js?v=2.0"></script>
    <script src="/src/client/scripts/modern-quotes-data.js?v=1.0"></script>
    <script src="/src/client/scripts/script.js?v=modern-quotes-001"></script>
    <script>
        // 浪漫星星功能
        let currentQuoteCategory = 'confession';

        // 初始化浪漫星星
        function initRomanticStars() {
            const stars = document.querySelectorAll('.romantic-star');
            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const category = this.getAttribute('data-quote-category');
                    currentQuoteCategory = category;
                    showRomanticQuote(category);
                });

                // 为每个星星添加随机位置变化功能
                initStarRandomPosition(star);
            });
        }

        // 为星星添加随机位置变化
        function initStarRandomPosition(star) {
            // 获取星星的动画持续时间
            const computedStyle = window.getComputedStyle(star);
            const animationDurations = computedStyle.animationDuration.split(',');
            const twinkleDuration = parseFloat(animationDurations[0]) * 1000; // 第一个动画（闪烁）的持续时间

            // 立即开始第一次位置变化循环
            schedulePositionChange(star, twinkleDuration);
        }

        // 安排位置变化
        function schedulePositionChange(star, duration) {
            // 在动画50%时（完全不透明时）改变位置
            setTimeout(() => {
                changeStarPosition(star);
            }, duration * 0.5);

            // 安排下一次位置变化
            setTimeout(() => {
                schedulePositionChange(star, duration);
            }, duration);
        }

        // 改变星星位置
        function changeStarPosition(star) {
            // 生成安全的随机位置（避免星星出现在边缘被裁剪）
            const minTop = 8;
            const maxTop = 85;
            const minLeft = 8;
            const maxLeft = 85;

            const randomTop = Math.random() * (maxTop - minTop) + minTop;
            const randomLeft = Math.random() * (maxLeft - minLeft) + minLeft;

            // 瞬间改变位置，不使用过渡效果（在完全透明时进行）
            star.style.transition = 'none';
            star.style.top = randomTop + '%';
            star.style.left = randomLeft + '%';

            // 恢复其他动画的过渡效果
            setTimeout(() => {
                star.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            }, 50);
        }

        // 显示浪漫话语
        function showRomanticQuote(category) {
            const quote = getRandomQuoteByCategory(category);
            updateQuoteDisplay(quote);
            document.getElementById('romanticModal').style.display = 'flex';
        }

        // 获取新的话语
        function getNewQuote() {
            const quote = getRandomQuoteByCategory(currentQuoteCategory);
            updateQuoteDisplay(quote);
        }

        // 更新诗词显示
        function updateQuoteDisplay(quote) {
            const quoteTextElement = document.getElementById('romanticQuoteText');
            const quoteAuthorElement = document.getElementById('romanticQuoteAuthor');
            const quoteWorkElement = document.getElementById('romanticQuoteWork');

            // 添加淡出效果
            quoteTextElement.style.opacity = '0';
            quoteAuthorElement.style.opacity = '0';
            quoteWorkElement.style.opacity = '0';

            setTimeout(() => {
                if (typeof quote === 'object' && quote.text) {
                    // 处理诗词换行：在"。"后添加换行
                    const formattedText = quote.text.replace(/。/g, '。<br>');
                    quoteTextElement.innerHTML = formattedText;
                    quoteAuthorElement.textContent = '—— ' + quote.author;
                    quoteWorkElement.textContent = quote.work;
                } else {
                    // 兼容旧格式
                    const formattedText = quote.replace(/。/g, '。<br>');
                    quoteTextElement.innerHTML = formattedText;
                    quoteAuthorElement.textContent = '—— 佚名';
                    quoteWorkElement.textContent = '古典诗词';
                }

                // 强制设置颜色
                quoteTextElement.style.color = '#ec4899';
                quoteTextElement.style.fontWeight = '600';

                // 添加淡入动画
                quoteTextElement.classList.remove('quote-fade-in');
                quoteAuthorElement.classList.remove('quote-fade-in');
                quoteWorkElement.classList.remove('quote-fade-in');

                // 强制重排，然后添加动画类
                void quoteTextElement.offsetWidth;

                quoteTextElement.classList.add('quote-fade-in');
                quoteAuthorElement.classList.add('quote-fade-in');
                quoteWorkElement.classList.add('quote-fade-in');

                // 恢复透明度
                quoteTextElement.style.opacity = '1';
                quoteAuthorElement.style.opacity = '1';
                quoteWorkElement.style.opacity = '1';
            }, 200);
        }

        // 关闭浪漫话语模态框
        function closeRomanticModal() {
            document.getElementById('romanticModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('romanticModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRomanticModal();
            }
        });

        // 折叠/展开留言板功能
        let messagesCollapsed = false;

        function toggleMessagesCollapse() {
            const messagesCollapsibleContent = document.getElementById('messagesCollapsibleContent');
            const collapseIcon = document.getElementById('collapseIcon');

            messagesCollapsed = !messagesCollapsed;

            if (messagesCollapsed) {
                messagesCollapsibleContent.style.display = 'none';
                collapseIcon.className = 'fas fa-chevron-right';
            } else {
                messagesCollapsibleContent.style.display = 'block';
                collapseIcon.className = 'fas fa-chevron-down';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initRomanticStars();

            // 确保留言板内容默认显示
            const messagesCollapsibleContent = document.getElementById('messagesCollapsibleContent');
            if (messagesCollapsibleContent) {
                messagesCollapsibleContent.style.display = 'block';
            }
        });

        // 智能视频加载器集成 - 四层CDN架构
        const USE_SMART_LOADER = true; // 新旧系统切换开关

        // 获取页面元素
        const video = document.querySelector('.video-background video');
        const videoContainer = document.querySelector('.video-background');
        loadingOverlay = document.getElementById('loadingOverlay'); // 重新赋值，避免重复声明
        const loadingProgress = document.getElementById('loadingProgress');

        // 视频加载状态标记 (保持兼容性)
        let videoInitialized = false;
        let videoLoadingStarted = false;

        // 隐藏加载遮罩 (保持原有函数)
        function hideLoadingOverlay() {
            console.log('🎭 开始隐藏加载遮罩:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
            if (loadingProgress) loadingProgress.style.width = '100%';
            setTimeout(function() {
                if (loadingOverlay) {
                    loadingOverlay.classList.add('hidden');
                    console.log('🎭 加载遮罩已隐藏:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
                }
            }, 300);
        }

        if (USE_SMART_LOADER && typeof VideoLoader !== 'undefined') {
            // 使用四层智能加载器
            console.log('🎬 启用四层智能视频加载器 - 首页');

            VideoLoader.integrateWithPage({
                pageName: 'home',
                videoSelector: '.video-background video',
                loadingOverlaySelector: '#loadingOverlay',
                loadingProgressSelector: '#loadingProgress'
            });

        } else if (video) {
            // 降级到原有加载逻辑 (保持完整兼容性)
            console.log('📼 使用原有视频加载逻辑 - 首页');
            loadVideoWithOriginalMethod();
        } else {
            // 如果没有视频元素，直接隐藏加载遮罩
            hideLoadingOverlay();
        }

        // 原有加载逻辑封装 (作为降级方案)
        function loadVideoWithOriginalMethod() {
            let progressInterval;
            let currentProgress = 0;

            // 模拟加载进度
            function updateProgress() {
                if (currentProgress < 90) {
                    currentProgress += Math.random() * 15;
                    if (currentProgress > 90) currentProgress = 90;
                    if (loadingProgress) loadingProgress.style.width = currentProgress + '%';
                }
            }

            // 开始进度模拟
            progressInterval = setInterval(updateProgress, 200);

            // 视频开始加载
            video.addEventListener('loadstart', function() {
                if (!videoLoadingStarted) {
                    console.log('🎬 首页花朵视频开始加载:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
                    videoLoadingStarted = true;

                    // 检查此时的背景状态
                    const videoContainer = document.querySelector('.video-background');
                    if (videoContainer) {
                        const beforeStyle = window.getComputedStyle(videoContainer, '::before');
                        console.log('🎬 视频开始加载时::before透明度:', beforeStyle.opacity);
                    }
                }
            });

            // 视频有足够数据可以播放
            video.addEventListener('canplay', function() {
                if (!videoInitialized) {
                    console.log('🎬 首页花朵视频可以播放:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
                    clearInterval(progressInterval);
                    currentProgress = 95;
                    if (loadingProgress) loadingProgress.style.width = currentProgress + '%';

                    // 确保视频自动播放
                    video.play().catch(function(error) {
                        console.log('🎬 视频自动播放被阻止:', error, '时间:', new Date().toISOString());
                        hideLoadingOverlay();
                    });
                }
            });

            // 视频加载完成并开始播放
            video.addEventListener('playing', function() {
                if (!videoInitialized) {
                    console.log('🎬 首页花朵视频背景加载成功并开始播放:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
                    clearInterval(progressInterval);
                    video.classList.add('loaded');
                    if (videoContainer) {
                        videoContainer.classList.add('video-loaded');

                        // 检查video-loaded类添加后的背景状态
                        setTimeout(() => {
                            const beforeStyle = window.getComputedStyle(videoContainer, '::before');
                            console.log('🎬 video-loaded后::before透明度:', beforeStyle.opacity);
                            console.log('🎬 视频元素透明度:', window.getComputedStyle(video).opacity);
                        }, 100);
                    }
                    hideLoadingOverlay();
                    videoInitialized = true;
                }
            });

            // 视频加载失败
            video.addEventListener('error', function() {
                if (!videoInitialized) {
                    console.log('🎬 首页花朵视频背景加载失败，使用花朵主题备用背景:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
                    clearInterval(progressInterval);
                    video.style.display = 'none';
                    if (videoContainer) {
                        const fallbackBg = 'linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%)';
                        videoContainer.style.background = fallbackBg;
                        videoContainer.classList.add('video-loaded');
                        console.log('🎬 设置备用背景:', fallbackBg);

                        // 检查备用背景设置后的状态
                        setTimeout(() => {
                            const containerStyle = window.getComputedStyle(videoContainer);
                            const beforeStyle = window.getComputedStyle(videoContainer, '::before');
                            console.log('🎬 备用背景设置后容器背景:', containerStyle.background.substring(0, 50) + '...');
                            console.log('🎬 备用背景设置后::before透明度:', beforeStyle.opacity);
                        }, 100);
                    }
                    hideLoadingOverlay();
                    videoInitialized = true;
                }
            });

            // 设置超时
            setTimeout(function() {
                if (!video.classList.contains('loaded')) {
                    console.log('🎬 首页视频加载超时，切换到花朵主题背景:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
                    clearInterval(progressInterval);
                    if (videoContainer) {
                        const timeoutBg = 'linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%)';
                        videoContainer.style.background = timeoutBg;
                        videoContainer.classList.add('video-loaded');
                        console.log('🎬 设置超时背景:', timeoutBg);

                        // 检查超时背景设置后的状态
                        setTimeout(() => {
                            const containerStyle = window.getComputedStyle(videoContainer);
                            const beforeStyle = window.getComputedStyle(videoContainer, '::before');
                            console.log('🎬 超时背景设置后容器背景:', containerStyle.background.substring(0, 50) + '...');
                            console.log('🎬 超时背景设置后::before透明度:', beforeStyle.opacity);
                        }, 100);
                    }
                    hideLoadingOverlay();
                }
            }, 10000);
        }

        // 页面卸载时保持视频资源 - 提供极速切换体验
        window.addEventListener('beforeunload', function(event) {
            console.log('🎬 首页保持视频资源不卸载，提供极速切换体验');
            // 完全移除视频清理逻辑，实现真正的视频持久化
            // 不再执行任何视频清理操作
        });

        // 页面隐藏时暂停视频，显示时恢复播放
        document.addEventListener('visibilitychange', function() {
            const video = document.querySelector('.video-background video');
            if (video && videoInitialized) {
                if (document.hidden) {
                    video.pause();
                    console.log('📱 页面隐藏，暂停视频播放');
                } else {
                    video.play().catch(function(error) {
                        console.log('📱 页面显示，恢复视频播放失败:', error);
                    });
                    console.log('📱 页面显示，恢复视频播放');
                }
            }
        });

        // 初始化字体加载器
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('🎨 开始初始化字体加载器...');

                // 初始化FontController
                if (typeof FontController !== 'undefined' && window.fontController) {
                    await window.fontController.init();
                    console.log('✅ FontController初始化完成');

                    // 预加载关键字体
                    const criticalFonts = ['Dancing Script', 'Poppins', 'Inter'];
                    for (const fontFamily of criticalFonts) {
                        try {
                            await window.fontController.loadFont(fontFamily);
                            console.log(`✅ 字体预加载成功: ${fontFamily}`);
                        } catch (error) {
                            console.warn(`⚠️ 字体预加载失败: ${fontFamily}`, error);
                        }
                    }
                } else {
                    console.warn('⚠️ FontController未找到，使用CSS声明的字体');
                }
            } catch (error) {
                console.error('❌ 字体加载器初始化失败:', error);
            }
        });
    </script>
</body>
</html>
